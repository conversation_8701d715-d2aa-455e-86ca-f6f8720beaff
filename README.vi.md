# Tăng cường trải nghiệm đọc "Edewakaru Japanese"

## Giới thiệu chức năng

Script này nhằm tối ưu hóa trải nghiệm đọc trên trang "Edewakaru Japanese" (https://www.edewakaru.com/).

---

## Chức năng cốt lõi

### 1. <PERSON>h giản bố cục trang

- Làm sạch trang: Tự động loại bỏ tiêu đề, chân trang, nút mạng xã hội, liên kết xếp hạng và quảng cáo.
- Tối ưu bố cục: Mở rộng vùng nội dung chính, cố định mục lục bên cạnh giúp chuyển trang dễ dàng khi đọc bài dài.

### 2. Hỗ trợ đọc

- Tự động chuyển đổi Furigana: Tự động nhận diện và chuyển đổi định dạng "từ (cách đọc)" thành hiển thị furigana chuẩn.
- Chuyển đổi hiển thị Furigana: Một nút bấm để bật/tắt trạng thái hiển thị furigana đã chuyển đổi.
- Đọc văn bản (TTS): Chọn văn bản rồi nhấn nút đọc, sử dụng tổng hợp giọng nói của trình duyệt để đọc.

### 3. Tối ưu hiệu năng & hình ảnh

- Thay thế hình ảnh chất lượng cao: Tự động thay thế ảnh mờ bằng ảnh gốc chất lượng cao, chỉ tải khi ảnh xuất hiện trong khung nhìn, tăng tốc độ và chất lượng hiển thị.
- Tải chậm bài viết liên quan: Mặc định chỉ tải "bài viết liên quan" khi cuộn đến gần. Có thể cài đặt thành "nhấn để tải" để giảm yêu cầu mạng.

### 4. Bảng cài đặt tùy chỉnh

Bảng cài đặt ở góc dưới bên phải trang, hỗ trợ các tùy chọn sau:

- [Tối ưu hóa trang]: Công tắc tổng cho chức năng script, các cài đặt khác dựa vào mục này. (Có hiệu lực sau khi làm mới)
- [Hiển thị Furigana]: Chuyển đổi trạng thái hiển thị furigana. (Hiệu lực tức thì)
- [Hiển thị bài viết liên quan]: Cài đặt chế độ tải "bài viết liên quan". Bật: tự động tải; Tắt: nhấn để tải. (Có hiệu lực sau khi làm mới)
- [Đọc văn bản chọn]: Bật/tắt chức năng đọc văn bản đã chọn, <b style="color:#A42121">chỉ hỗ trợ trình duyệt Microsoft Edge</b>. (Hiệu lực tức thì)

---

## Phản hồi vấn đề

Nếu gặp các trường hợp sau, vui lòng gửi phản hồi qua trang script Greasy Fork:

- Lỗi chuyển đổi furigana hoặc từ chưa được hỗ trợ
- Lỗi chức năng
- Đề xuất cải tiến

Vui lòng cung cấp thông tin sau để giúp xác định và sửa lỗi:

1. URL bài viết gặp vấn đề
2. Từ hoặc khu vực cụ thể bị lỗi hoặc bất thường

Phản hồi của bạn là động lực để script ngày càng hoàn thiện. Xin cảm ơn!
