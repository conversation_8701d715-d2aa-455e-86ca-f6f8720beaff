# 「絵でわかる日本語」読書体験強化

## 機能概要

このスクリプトは「絵でわかる日本語」（https://www.edewakaru.com/）の読書体験を最適化します。

---

## コア機能

### 1. ページレイアウトの簡素化

- ページの浄化：ヘッダー、フッター、SNSボタン、ランキングリンク、広告を自動で削除します。
- レイアウト最適化：メインコンテンツ領域を拡大し、サイドバー目次を固定。長文閲覧時のジャンプが容易になります。

### 2. 読書補助機能

- ふりがな自動変換：「単語（読み）」形式を自動認識し、標準的なふりがな表示に変換します。
- ふりがな表示切替：変換されたふりがなの表示状態をワンクリックで切り替え可能。
- 選択テキスト音声読み上げ（TTS）：テキスト選択後、読み上げボタンをクリックすると、ブラウザの音声合成で朗読します。

### 3. パフォーマンスと視覚最適化

- 高画質画像への置換：ぼやけたサムネイル画像を高画質原画像に自動置換。画像がビューポートに入った時のみ読み込み、表示速度と品質を向上。
- 関連記事の遅延読み込み：スクロールで近くに来た時のみ「関連記事」等の埋め込みコンテンツを読み込み。クリックで読み込みに設定可能、ネットワークリクエストを削減。

### 4. カスタム設定パネル

画面右下に設定パネルを提供し、以下の設定項目をサポート：

- 【ページ最適化】：スクリプト機能のメインスイッチ。他の設定はこの項目が有効時のみ動作。（リフレッシュ後有効）
- 【ふりがな表示】：ふりがな表示状態の切り替え。（即時有効）
- 【関連記事表示】：「関連記事」の読み込みモードを設定。オンで自動読み込み、オフでクリック読み込み。（リフレッシュ後有効）
- 【単語選択発音】：選択テキスト読み上げ機能のオン/オフ。<b style="color:#A42121">Microsoft Edgeブラウザのみ対応</b>。（即時有効）

---

## 問題のフィードバック

以下の場合はGreasy Forkスクリプトページからフィードバックをお願いします：

- ふりがな変換ミスや未対応語彙
- 機能の不具合
- 改善提案

問題特定・修正のため、可能な限り以下の情報をご提供ください：

1. 問題が発生した記事のURL
2. 誤りや不具合の具体的な語句や箇所

ご協力ありがとうございます！
