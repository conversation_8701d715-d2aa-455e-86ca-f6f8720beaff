# "Edewakaru Japanese" Reading Experience Enhancement

## Feature Overview

This script aims to optimize the reading experience on "Edewakaru Japanese" (https://www.edewakaru.com/).

---

## Core Features

### 1. Streamlined Page Layout

- Page Purification: Automatically removes headers, footers, social media buttons, ranking links, and ads.
- Layout Optimization: Expands the main content area and fixes the sidebar table of contents for easier navigation in long articles.

### 2. Reading Assistance

- Automatic Furigana Conversion: Automatically detects and converts "word (reading)" format to standard furigana display.
- Furigana Toggle: One-click switch to show or hide all converted furigana.
- Text-to-Speech (TTS): Select text and click the read-aloud button to use browser speech synthesis for reading.

### 3. Performance & Visual Optimization

- High-Resolution Image Replacement: Automatically replaces blurry thumbnails with high-res originals, loading images only when they enter the viewport for faster loading and better quality.
- Delayed Related Articles Loading: "Related articles" and embedded content load only when scrolled near. Can be set to "load on click" to reduce network requests.

### 4. Custom Settings Panel

A settings panel is provided at the bottom right of the page, supporting the following options:

- [Page Optimization]: Master switch for script features; other settings depend on this. (Takes effect after refresh)
- [Furigana Display]: Toggle furigana visibility. (Instant effect)
- [Related Articles Display]: Set loading mode for "related articles". On: auto-load; Off: load on click. (Takes effect after refresh)
- [Text Selection TTS]: Toggle text-to-speech for selected words. <b style="color:#A42121">Only supported in Microsoft Edge browser</b>. (Instant effect)

---

## Feedback

If you encounter any of the following, please submit feedback via the Greasy Fork script page:

- Furigana conversion errors or missing words
- Feature malfunctions
- Suggestions for improvement

Please provide the following information to help locate and fix issues:

1. The article URL where the issue occurred
2. The specific word or area with the error or problem

Your feedback is essential for continuous improvement. Thank you!
