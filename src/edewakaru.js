// ==UserScript==
// @name                 Edewakaru Enhanced
// @name:jp              「絵でわかる日本語」 閲覧体験強化
// @name:zh-CN           「絵でわかる日本語」 阅读体验增强
// @name:zh-TW           「絵でわかる日本語」 閱讀體驗增強
// @namespace            https://greasyfork.org/users/49949-ipumpkin
// @version              2025.07.12
// <AUTHOR>
// @description          Enhances reading experience on the "絵でわかる日本語" site by converting kanji readings from parentheses to ruby, hiding ads and clutter, and adding text-to-speech for selected text.
// @description:jp       「絵でわかる日本語」サイト内の漢字の読みを括弧表記から自動でふりがなに変換し、広告や不要な要素を非表示にします。選択テキストの読み上げ機能にも対応し、快適な読書体験を実現します。
// @description:zh-CN    将「絵でわかる日本語」网站中的汉字注音由括号形式自动转换为振假名，隐藏广告和无关元素，并支持划词朗读功能，提升阅读体验。
// @description:zh-TW    將「絵でわかる日本語」網站中的漢字注音由括號形式自動轉換為振假名，隱藏廣告與無關元素，並支援劃詞朗讀功能，提升閱讀體驗。
// @license              GPL-3.0
// @icon                 https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// @match                https://www.edewakaru.com/*
// @grant                GM_addStyle
// @grant                GM_getValue
// @grant                GM_setValue
// @run-at               document-start
// ==/UserScript==

;(function () {
  ;('use strict')

  // ===================================================================================
  // C O N F I G U R A T I O N
  // 集中存放所有需要便捷修改的数据
  // ===================================================================================
  const RULES = {
    // 文本层处理规则 (在纯文本节点上操作)
    TEXT: {
      // 全自动：提供模式，由脚本智能分割汉字和假名
      AUTO: [
        'km（キロ）',
        'm（メートル）',
        '℃（ど）',
        'お団子（おだんご）',
        'お年寄り（おとしより）',
        'お店（おみせや）',
        'お茶する（おちゃする）',
        'ご先祖さま（ごせんぞさま）',
        '一つ（ひとつ）',
        '万引き（まんびき）',
        '三分の一（さんぶんのいち）',
        '不確か（ふたしか）',
        '不足（ふそく）',
        '世界１周旅行（せかいいっしゅうりょこう）',
        '中（じゅう）',
        '以上（いじょう）',
        '以外（いがい）',
        '住（す）',
        '使い分け（つかいわけ）',
        '使い方（つかいかた）',
        '使用（しよう）',
        '働（はたら）',
        '元を取る（もとをとる）',
        '元カノ（もとかの）',
        '元カレ（もとかれ）',
        '入学（にゅうがく）',
        '入（はい）',
        '全て（すべて）',
        '出張 （しゅっちょう）',
        '出張（しゅっちょう）',
        '分（ぶん）',
        '前（まえ）',
        '動作（どうさ）',
        '口の中（くちのなか）',
        '合（あ）',
        '吐き気（はきけ）',
        '味覚 （みかく）',
        '呼び方（よびかた）',
        '唐揚げ（からあげ）',
        '商品（しょうひん）',
        '土砂崩れ（どしゃくずれ）',
        '夏休み中（なつやすみちゅう）',
        '夏祭り（なつまつり）',
        '夕ご飯（ゆうごはん）',
        '大切（たいせつ）',
        '大好き（だいすき）',
        '学習者（がくしゅうしゃ）',
        '宝くじ（たからくじ）',
        '寝る前（ねるまえ）',
        '寝（ね）',
        '届け出（とどけで）',
        '座り心地（すわりごこち）',
        '引っ越す（ひっこす）',
        '当たり前（あたりまえ）',
        '役に立つ（やくにたつ）',
        '待（ま）',
        '後ろ（うしろ）',
        '怒り（いかり）',
        '思い出す（おもいだす）',
        '恵方巻き（えほうまき）',
        '悩み事（なやみごと）',
        '感じ方（かんじかた）',
        '戦（せん）',
        '手作り（てづくり）',
        '折があれば（おりがあれば）',
        '折に触れて（おりにふれて）',
        '折も折（おりもおり）',
        '折を見て（おりをみて）',
        '数え方（かぞえかた）',
        '文化（ぶんか）',
        '文法（ぶんぽう）',
        '旅行（りょこう）',
        '日記（にっき）',
        '早寝早起き（はやねはやおき）',
        '星の数ほどある（ほしのかずほどある）',
        '星の数ほどいる（ほしのかずほどいる）',
        '星の数（ほしのかず）',
        '昭和の日（しょうわのひ）',
        '暮（ぐ）',
        '有名（ゆうめい）',
        '梅雨入り（つゆいり）',
        '楽（たの）',
        '歩（ある）',
        '残業（ざんぎょう）',
        '気を付けて（きをつけて）',
        '気持ち（きもち）',
        '独り言（ひとりごと）',
        '瓜二つ（うりふたつ）',
        '甘い物（あまいもの）',
        '申し訳（もうしわけ）',
        '盗み食い（ぬすみぐい）',
        '真っ暗（まっくら）',
        '真ん中（まんなか）',
        '知り合い（しりあい）',
        '確か（たしか）',
        '社会（しゃかい）',
        '福笑い（ふくわらい）',
        '窓の外（まどのそと）',
        '立ち読み（たちよみ）',
        '第２月曜日（だいにげつようび）',
        '笹の葉（ささのは）',
        '細長い（ほそながい）',
        '紹介（しょうかい）',
        '組み合わせ（くみあわせ）',
        '経（た）',
        '結婚（けっこん）',
        '繰り返して（くりかえして）',
        '羽根つき（はねつき）',
        '考え方（かんがえかた）',
        '聞き手（ききて）',
        '腹が立つ（はらがたつ）',
        '自身（じしん）',
        '芸術の秋（げいじゅつのあき）',
        '落ち着（おちつ）',
        '行き方（いきかた）',
        '行き渡る（いきわたる）',
        '触り心地（さわりごこち）',
        '試験（しけん）',
        '話し手（はなして）',
        '話し言葉（はなしことば）',
        '読み方（よみかた）',
        '読書の秋（どくしょのあき）',
        '請け合い（うけあい）',
        '豪雨（ごうう）',
        '貯金（ちょきん）',
        '貯（た）',
        '買い物（かいもの）',
        '貸し借り（かしかり）',
        '足が早い（あしがはやい）',
        '通り（とおり）',
        '通り（どおり）',
        '通知（つうち）',
        '通（どお）',
        '連続（れんぞく）',
        '遅刻（ちこく）',
        '長い間（ながいあいだ）',
        '長生き（ながいき）',
        '雨の日（あめのひ）',
        '青い色（あおいいろ）',
        '青のり（あおのり）',
        '願い事（ねがいごと）',
        '食べず嫌い（たべずぎらい）',
        '食べ物（たべもの）',
        '食欲の秋（しょくよくのあき）',
        '食（しょく）',
        '飲み会（のみかい）',
        '飲み物（のみもの）',
        '駅（えき）',
        '驚き（おどろき）',
        '髪の毛（かみのけ）',
        '鳴き声（なきごえ）',
        '０点（れいてん）',
        '１か月間（いっかげつかん）',
        '１か月（いっかげつ）',
        '１つ（ひとつ）',
        '１人（ひとり）',
        '１列（いちれつ）',
        '１回（いっかい）',
        '１年（いちねん）',
        '１度（いちど）',
        '１日中（いちにちじゅう）',
        '１日（ついたち）',
        '１杯（いっぱい）',
        '１泊（いっぱく）',
        '１０日間（とおかかん）',
        '１０日（とおか）',
        '１０杯（じゅっぱい）',
        '２人（ふたり）',
        '２日（ふつか）',
        '３日間（みっかかん）',
        '３日（みっか）',
        '３杯（さんばい）',
        '５分（ごふん）',
        '５日間（いつかかん）',
        '５月（ごがつ）',
        '７日（なのか）',
      ],

      // 半自动：提供模式和最终的读音，用于特殊读音
      READING: [
        { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
        { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
        { pattern: '付き合（つきあい）', reading: 'つきあ' },
        { pattern: 'コマ回し（こままわし）', reading: 'コマまわし' },
        { pattern: '今回（今回）', reading: 'こんかい' },
        { pattern: '一般的（いっぱん）', reading: 'いっぱんてき' },
        { pattern: '必ず（かなら）', reading: 'かならず' },
        { pattern: '青リンゴ（あおりんご）', reading: 'あおリンゴ' },
        { pattern: '食べ物（食べ物）', reading: 'たべもの' },
      ],

      // 全手动：提供模式和最终的 HTML，用于最复杂的送假名等情况
      FULL: [
        { pattern: 'マイ〇〇（my+〇〇）', replacement: '<ruby>マイ<rt>my</rt></ruby>〇〇' },
        { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
        { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
        { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
        { pattern: '年越しそば（としこしそば）', replacement: '<ruby>年越<rt>としこ</rt></ruby>しそば' },
        { pattern: '原因・理由（げんいん・りゆう）', replacement: '<ruby>原因<rt>げんいん</rt></ruby>・<ruby>理由<rt>りゆう</rt></ruby>' },
        { pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）', replacement: '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える' },
        { pattern: '青菜・青野菜（あおな・あおやさい）', replacement: '<ruby>青菜<rt>あおな</rt></ruby>・<ruby>青野菜<rt>あおやさい</rt></ruby>' },
        { pattern: '水の泡になる・水の泡となる（みずのあわになる）', replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる' },
        { pattern: '意味で（いみ）', replacement: '<ruby>意味<rt>いみ</rt></ruby>で' },
        { pattern: '和製英語で（わせいえいご）', replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で' },
        { pattern: '財布を（さいふ）', replacement: '<ruby>財布<rt>さいふ</rt></ruby>を' },
        { pattern: '夏バテ防止（なつばてぼうし）', replacement: '<ruby>夏<rt>なつ</rt></ruby>バテ<ruby>防止<rt>ぼうし</rt></ruby>' },
        { pattern: 'ソーシャル・ネットワーキング・サービス（Social Networking Service）', replacement: '<ruby>ソーシャル<rt>Social</rt></ruby>・<ruby>ネットワーキング<rt>Networking</rt></ruby>・<ruby>サービス<rt>Service</rt></ruby>' },
      ],
    },

    // 使用正则表达式，直接在 innerHTML 上操作，用于处理跨节点等疑难杂症
    HTML: [
      { pattern: /一瞬（いっしゅん<br>）/g, replacement: '<ruby>一瞬<rt>いっしゅん</rt></ruby>' },
      { pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g, replacement: '<b><ruby>居<rt>い</rt></ruby></b>' },
      { pattern: /<b style="font-size: large;">留守<\/b>（るす）/g, replacement: '<b><ruby>留守<rt>るす</rt></ruby></b>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>次第<\/b><\/span>（しだい）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b><ruby>次第<rt>しだい</rt></ruby></b></span>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>から、当然<\/b><\/span>（とうぜん）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b>から、<ruby>当然<rt>とうぜん</rt></ruby></b></span>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>生きがい<\/b><\/span>（いきがい）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b><ruby>生<rt>い</rt></ruby>きがい</b></span>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>教えがい<\/b><\/span>（おしえがい）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b><ruby>教<rt>おし</rt></ruby>えがい</b></span>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>育てがい<\/b><\/span>（そだてがい）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b><ruby>育<rt>そだ</rt></ruby>てがい</b></span>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>作りがい<\/b><\/span>（つくりがい）/g, replacement: '<span style="color: rgb(255, 0, 0);"><b><ruby>作<rt>つく</rt></ruby>がい</b></span>' },
      { pattern: /<span style="color: rgb\(255, 0, 0\);"><b>だけの状態<\/b><\/span><b><span style="color: rgb\(255, 0, 0\);">（じょうたい）だ<\/span><\/b>/g, replacement: '<span style="color: rgb(255, 0, 0);"><b>だけの<ruby>状態<rt>じょうたい</rt></ruby>だ</b></span>' },
      { pattern: /<span style="background-color: rgb\(204, 204, 204\);">運動<\/span>（うんどう）/g, replacement: '<span style="background-color: rgb(204, 204, 204);"><ruby>運動<rt>うんどう</rt></ruby></span>' },
      { pattern: /<b style="background-color: rgb\(255, 255, 0\);"><span style="color: rgb\(255, 0, 0\);">「エアコン」とはエアーコンディショナー（<\/span><\/b>/g, replacement: '<b style="background-color: rgb(255, 255, 0);"><span style="color: rgb(255, 0, 0);">「エアコン」とは<ruby>エアーコンディショナー<rt>air conditioner</rt></ruby></span></b>' },
      { pattern: /air conditioner）/g, replacement: '' },
      { pattern: /<b>書（か）き言葉（ことば）的<\/b>（てき）/g, replacement: '<b><ruby>書<rt>か</rt></ruby>き<ruby>言葉的<rt>ことばてき</rt></ruby></b>' },
    ],

    // 排除规则 用于防止脚本对特定内容进行错误的注音转换
    EXCLUDE: {
      // 全局排除：匹配完整的字符串，例如 '人称（私）'
      STRINGS: new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）', '女性（おばあちゃん）']),

      // 助词排除：当括号前是纯假名时，排除特定的助词
      PARTICLES: new Set(['は', 'が', 'を', 'に', 'で', 'と', 'から', 'まで', 'へ', 'より', 'の', 'て', 'し', 'も', 'や', 'ね', 'よ', 'さ', 'あ', 'な']),
    },
  }

  /**
   * @module PageOptimizer
   * @description 负责页面布局优化、样式注入和无关元素清理
   * 1 立即执行：在页面加载初期 document-start，立即注入 CSS 规则，隐藏所有已知的不需要元素，实现无闪烁的视觉体验
   * 2 延迟清理：在文档对象模型完全加载后 DOMContentLoaded，从结构上彻底移除所有被隐藏的元素以及页面中的脚本
   */
  const PageOptimizer = {
    _config: {
      MODULE_ENABLED: true,
      GLOBAL_REMOVE_SELECTORS: ['header#blog-header', 'footer#blog-footer', '.ldb_menu', '#analyzer_tags', '#gdpr-banner', '.adsbygoogle', '#ad_rs', '#ad2', 'div[class^="fluct-unit"]', '.article-social-btn', 'iframe[src*="clap.blogcms.jp"]', '#article-options', 'a[href*="blogmura.com"]', 'a[href*="with2.net"]', 'div[id^="ldblog_related_articles_"]'],
      STYLES: `
        #container { width: 100%; }
        @media (min-width: 960px) { #container { max-width: 960px; } }
        @media (min-width: 1040px) { #container { max-width: 1040px; } }
        #content { display: flex; position: relative; padding: 50px 0 !important; }
        #main { flex: 1; float: none !important; width: 100% !important; }
        aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }
        .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; }
        .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; }
        .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }
        .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; }
        .article-body { padding: 0 !important; }
        .article-pager { margin-bottom: 0 !important; }
        .article-body-inner { line-height: 2; opacity: 0; transition: opacity 0.3s; }
        .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; }
        .article-body-inner strike { color: orange !important; }
        .to-pagetop { position: fixed; bottom: 19.2px; right: 220px; z-index: 9999; }
        rt, iframe, time, .pager, #sidebar { -webkit-user-select: none; user-select: none; }
        .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }
      `,
    },
    init() {
      if (!this._config.MODULE_ENABLED) return

      const antiFlickerCss = `${this._config.GLOBAL_REMOVE_SELECTORS.join(', ')} { display: none !important; }`
      GM_addStyle(antiFlickerCss)
      GM_addStyle(this._config.STYLES)
    },
    /**
     * 负责执行一次性的全局 DOM 清理任务
     * @private
     */
    cleanupGlobalElements() {
      if (!this._config.MODULE_ENABLED) return
      // 1. 根据配置列表，移除所有匹配的垃圾组件
      document.querySelectorAll(this._config.GLOBAL_REMOVE_SELECTORS.join(',')).forEach((el) => el.remove())
      // 2. 移除 <body> 内所有不再需要的、可能引起副作用的标签
      document.querySelectorAll('body script, body link, body style, body noscript').forEach((el) => el.remove())
    },
    /**
     * 负责单篇文章容器的收尾工作
     * @param {HTMLElement} container - 文章 `.article-body-inner` 容器元素
     */
    cleanupArticleBody(container) {
      if (!this._config.MODULE_ENABLED) return
      // 清理外层容器本身的头尾
      this._trimContainerBreaks(container)
      // 找到最后一个子元素，清理它的头尾
      const lastElement = container.lastElementChild
      if (lastElement) {
        this._trimContainerBreaks(lastElement)
      }
      container.style.opacity = 1
    },
    /**
     * 清理容器开头和结尾多余的换行和空白节点
     * @param {HTMLElement} element - 任何需要被清理头尾的 DOM 元素
     * @private
     */
    _trimContainerBreaks(element) {
      // 安全检查，如果传入的不是一个有效元素，则直接返回
      if (!element) return
      // 判断节点是否为“垃圾”
      const isJunkNode = (node) => {
        if (!node) return true
        // 检查是否为“纯空白”的文本节点
        if (node.nodeType === 3 && /^\s*$/.test(node.textContent)) {
          return true
        }
        // 检查是否为元素节点，并且是我们定义的“垃圾”标签
        if (node.nodeType === 1) {
          const tagName = node.tagName
          if (tagName === 'BR') return true
          if (tagName === 'SPAN' && /^\s*$/.test(node.textContent)) return true
          if (tagName === 'A' && /^\s*$/.test(node.textContent)) return true
        }
        return false
      }
      // 从开头移除所有垃圾节点
      while (element.firstChild && isJunkNode(element.firstChild)) {
        element.removeChild(element.firstChild)
      }
      // 从结尾移除所有垃圾节点
      while (element.lastChild && isJunkNode(element.lastChild)) {
        element.removeChild(element.lastChild)
      }
    },
    /**
     * 优化侧边栏，只保留分类并使其可见
     */
    finalizeLayout() {
      if (!this._config.MODULE_ENABLED) return
      const sidebar = document.querySelector('aside#sidebar')
      if (!sidebar) return
      const category = sidebar.querySelector('.plugin-categorize')
      // 清空侧边栏现有内容
      sidebar.innerHTML = ''
      if (category) {
        // 只将分类插件加回去
        sidebar.appendChild(category)
        // 使侧边栏可见
        sidebar.style.visibility = 'visible'
      }
    },
  }

  /**
   * @module ImageProcessor
   * @description 专门处理博客图片链接，将其转换为直接的图片元素
   * 此模块查找页面中指向 livedoor 图床的链接，并将它们替换为高质量的 `<img>` 标签，
   * 从而优化图片加载和显示体验
   */
  const ImageProcessor = {
    _config: {
      MODULE_ENABLED: true,
      // 匹配 livedoor 缩略图链接的正则表达式
      IMG_SRC_REGEX: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
    },
    /**
     * 处理指定容器内的所有图片链接
     * @param {HTMLElement} container - 包含图片链接的容器元素
     */
    process(container) {
      if (!this._config.MODULE_ENABLED) return
      container.querySelectorAll('a[href*="livedoor.blogimg.jp"]').forEach((link) => {
        const img = link.querySelector('img.pict')
        if (!img) return
        // 创建新的图片元素
        const newImg = document.createElement('img')
        newImg.loading = 'lazy'
        // 移除 '-s' 后缀以获取原图
        newImg.src = img.src.replace(this._config.IMG_SRC_REGEX, '$1$2')
        // 继承原有属性
        newImg.alt = (img.alt || '').replace(/blog/gi, '')
        Object.assign(newImg, { className: img.className, width: img.width, height: img.height })
        // 用新的图片元素替换整个链接
        link.replaceWith(newImg)
      })
    },
  }

  /**
   * @module IframeLoader
   * @description 负责 iframe 的加载策略管理
   * - lazy (懒加载) 策略: 结合浏览器原生 loading="lazy" 和 CSS 样式，实现高性能懒加载及视觉反馈
   * - click (点击加载) 策略: 将 iframe 替换为点击后才加载的占位符，实现极致的初始加载性能
   * - eager (默认) 策略: 不做任何干预，由浏览器默认处理
   */
  const IframeLoader = {
    _config: {
      MODULE_ENABLED: true,
      IFRAME_LOAD_ENABLED: true, // 可以从 setting 中获取覆盖
      IFRAME_SELECTOR: 'iframe[src*="richlink.blogsys.jp"]',
      PLACEHOLDER_CLASS: 'iframe-placeholder',
      LOADING_CLASS: 'is-loading',
      CLICKABLE_CLASS: 'is-clickable',
      STYLES: `
        @keyframes iframe-spinner-rotation { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .iframe-placeholder { position: relative; display: inline-block; vertical-align: top; background-color: #f9f9f9; box-sizing: border-box; margin: 8px 0; }
        .is-loading::after { opacity: 0.9; content: ''; position: absolute; top: 50%; left: 50%; width: 32px; height: 32px; margin-top: -16px; margin-left: -16px; border: 4px solid #ccc; border-top-color: #3B82F6; border-radius: 50%; animation: iframe-spinner-rotation 1s linear infinite; }
        .is-clickable { opacity: 0.9; display: inline-grid; place-items: center; color: #ccc; font-weight: bold; font-size: 16px; cursor: pointer; transition: background-color 0.2s, color 0.2s; -webkit-user-select: none; user-select: none; }
        .is-clickable:hover { opacity: 0.9; color: #3B82F6; background-color: #f4f8ff; }
        @media screen and (max-width: 870px) { .iframe-placeholder { max-width: 350px !important; height: 105px !important; } }
        @media screen and (min-width: 871px) { .iframe-placeholder { max-width: 580px !important; height: 120px !important; } }
      `,
    },
    init(options) {
      if (!this._config.MODULE_ENABLED) return
      Object.assign(this._config, options)
      GM_addStyle(this._config.STYLES)
    },
    processContainer(container) {
      if (!this._config.MODULE_ENABLED) return
      const iframes = container.querySelectorAll(this._config.IFRAME_SELECTOR)
      if (iframes.length === 0) return
      this._config.IFRAME_LOAD_ENABLED ? this._processForLazyLoad(iframes) : this._processForClickToLoad(iframes)
    },
    /**
     * 使用 IntersectionObserver 实现高性能、高可靠的懒加载
     */
    _processForLazyLoad(iframes) {
      // 1. 创建一个观察者实例，用于监视所有iframe占位符
      const observer = new IntersectionObserver(
        (entries, obs) => {
          entries.forEach((entry) => {
            // 当占位符进入或即将进入视口时
            if (entry.isIntersecting) {
              const placeholder = entry.target
              // 动态创建真正的 iframe
              const iframe = document.createElement('iframe')
              iframe.src = placeholder.dataset.src
              iframe.setAttribute('style', placeholder.dataset.style)
              iframe.setAttribute('frameborder', '0')
              iframe.setAttribute('scrolling', 'no')
              iframe.style.opacity = '0' // 初始保持透明
              iframe.addEventListener(
                'load',
                () => {
                  placeholder.classList.remove(this._config.LOADING_CLASS)
                  iframe.style.opacity = '1'
                },
                { once: true },
              )
              // 将 iframe 添加到占位符中
              placeholder.appendChild(iframe)
              // 关键优化：一旦处理完毕，就立刻停止观察此占位符，释放资源
              obs.unobserve(placeholder)
            }
          })
        },
        {
          // 预加载边距：元素距离视口底部200px时即开始加载
          rootMargin: '200px 0px',
        },
      )

      // 2. 遍历所有找到的 iframe，用占位符替换它们
      iframes.forEach((iframe) => {
        const placeholder = document.createElement('div')
        placeholder.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.LOADING_CLASS}`
        const originalStyle = iframe.getAttribute('style') || ''
        placeholder.setAttribute('style', originalStyle)
        // 将 iframe 的重要信息缓存到占位符的 data-* 属性中
        placeholder.dataset.src = iframe.src
        placeholder.dataset.style = originalStyle
        // 用占位符替换原始 iframe
        iframe.replaceWith(placeholder)
        // 让观察者开始监视这个新的占位符
        observer.observe(placeholder)
      })
    },
    _processForClickToLoad(iframes) {
      iframes.forEach((iframe) => {
        if (iframe.parentElement.classList.contains(this._config.PLACEHOLDER_CLASS)) return
        const originalSrc = iframe.src
        const originalStyle = iframe.getAttribute('style') || ''
        const placeholder = document.createElement('div')
        placeholder.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.CLICKABLE_CLASS}`
        placeholder.textContent = '▶ 関連記事を読み込む'
        placeholder.setAttribute('style', originalStyle)
        placeholder.addEventListener(
          'click',
          () => {
            const newIframe = document.createElement('iframe')
            newIframe.src = originalSrc
            newIframe.setAttribute('style', originalStyle)
            newIframe.setAttribute('frameborder', '0')
            newIframe.setAttribute('scrolling', 'no')
            const loadingWrapper = document.createElement('div')
            loadingWrapper.className = `${this._config.PLACEHOLDER_CLASS} ${this._config.LOADING_CLASS}`
            loadingWrapper.setAttribute('style', originalStyle)
            newIframe.style.opacity = '0'
            loadingWrapper.appendChild(newIframe)
            newIframe.addEventListener(
              'load',
              () => {
                loadingWrapper.classList.remove(this._config.LOADING_CLASS)
                newIframe.style.opacity = '1'
              },
              { once: true },
            )
            placeholder.replaceWith(loadingWrapper)
          },
          { once: true },
        )
        iframe.replaceWith(placeholder)
      })
    },
  }

  /**
   * @module RubyConverter
   * @description 封装所有关于注音转换的逻辑，包括词库、规则和 DOM 处理
   * 这是脚本的核心功能模块，它遵循一个明确的优先级顺序来转换文本：
   * 优先级 0: HTML 级别强制替换 (HTML)
   * 优先级 1: 手动指定的特例词汇 (TEXT.AUTO, TEXT.READING, TEXT.FULL)
   * 优先级 2: bracket 动态学习的词汇 (从【...】或「...」中提取)
   * 优先级 3: katakana片假名(英文)模式
   * 优先级 4: ruby 通用汉字(注音)模式，并应用各种排除规则
   */
  const RubyConverter = {
    _config: {
      MODULE_ENABLED: true,
    },
    _rules: null,
    _regex: {
      bracket: /[【「](?:.*?)([^【】「」（）・、\s～〜]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
      katakana: /([ァ-ンー]+)[（(]([\w\s+]+)[）)]/g,
      ruby: /([一-龯々]+)\s*[(（]([^（）()]*)[)）]/g,

      kanaOnly: /^[\u3040-\u309F]+$/,
      nonKana: /[^\u3040-\u309F]/,
      isKanaChar: /^[\u3040-\u309F]$/,
      hasInvalidChars: /[^一-龯々\u3040-\u309F\u30A0-\u30FF]/,
    },
    // 预处理后的词库和动态学习的词汇
    _processedWords: { patternResults: new Map(), globalRegex: null },
    _dynamicWords: new Set(),
    /**
     * 初始化模块，接收外部配置并预处理词库以提高性能
     * @param {object} rules - 包含所有规则和词汇列表的外部配置对象
     */
    init(rules) {
      if (!this._config.MODULE_ENABLED) return

      this._rules = rules
      this._preprocessWords(rules)
    },
    /**
     * 处理指定容器，按优先级顺序应用所有转换规则
     * @param {HTMLElement} container - 需要处理的根元素
     */
    processContainer(container) {
      if (!this._config.MODULE_ENABLED) return

      this._applyHtmlReplacements(container) // 优先级 0
      this._findAndRegisterCompounds(container) // 优先级 2 (学习过程)
      this._processRubyInNodes(container) // 应用 优先级 1, 3, 4
    },
    // 辅助函数：转义正则特殊字符
    _escapeRegExp: (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
    /**
     * 解析不同格式的特例词条
     * @param {string|object} entry - 原始词条
     * @returns {object|null} - 解析后的对象或 null
     */
    _parseCompoundEntry(entry) {
      if (typeof entry === 'string') {
        const match = entry.match(/(.*?)（(.*?)）/)
        if (match) return { pattern: entry, kanji: match[1].trim(), reading: match[2] }
      } else if (entry.reading) {
        return { pattern: entry.pattern, kanji: entry.pattern.replace(/（.*?）/, ''), reading: entry.reading }
      } else if (entry.replacement) {
        return entry
      }
      return null
    },
    /**
     * 预处理所有特例词，生成替换结果并构建全局正则表达式
     * @param {object} rules - 完整的 RULES 对象
     */
    _preprocessWords(rules) {
      const allPatterns = []
      // 1. 处理标准词汇: '汉字（注音）'
      rules.TEXT.AUTO.forEach((entry) => {
        const match = entry.match(/(.*?)（(.*?)）/)
        if (!match) return
        const pattern = entry
        const kanji = match[1].trim()
        const reading = match[2]
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, this._segmentCompoundWord(kanji, reading))
      })
      // 2. 处理强制注音: { pattern, reading }
      rules.TEXT.READING.forEach((entry) => {
        const { pattern, reading } = entry
        const kanji = pattern.replace(/（.*?）/, '')
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, this._segmentCompoundWord(kanji, reading))
      })
      // 3. 处理强制替换: { pattern, replacement }
      rules.TEXT.FULL.forEach((entry) => {
        const { pattern, replacement } = entry
        allPatterns.push(this._escapeRegExp(pattern))
        this._processedWords.patternResults.set(pattern, replacement)
      })

      // 最后，根据收集到的所有模式构建全局正则表达式
      this._rebuildGlobalRegex(allPatterns)
    },
    /**
     * 根据模式列表重新构建全局正则表达式
     * @param {Array<string>} patterns - 正则表达式模式字符串数组
     */
    _rebuildGlobalRegex(patterns) {
      this._processedWords.globalRegex = patterns.length > 0 ? new RegExp(`(${patterns.join('|')})`, 'g') : null
    },
    /**
     * 智能分割复合词的汉字和读音，生成 Ruby 标签
     * @param {string} kanji - 汉字部分 (可能包含假名)
     * @param {string} reading - 读音部分
     * @returns {string} - 生成的 HTML Ruby 字符串
     */
    _segmentCompoundWord(kanji, reading) {
      let result = '',
        kanjiIndex = 0,
        readingIndex = 0
      while (kanjiIndex < kanji.length) {
        if (this._regex.isKanaChar.test(kanji[kanjiIndex])) {
          // 如果是假名，直接附加
          result += kanji[kanjiIndex]
          readingIndex = reading.indexOf(kanji[kanjiIndex], readingIndex) + 1
          kanjiIndex++
        } else {
          // 处理连续的汉字块
          let kanjiPart = ''
          while (kanjiIndex < kanji.length && !this._regex.isKanaChar.test(kanji[kanjiIndex])) {
            kanjiPart += kanji[kanjiIndex++]
          }
          const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length
          result += `<ruby>${kanjiPart}<rt>${reading.substring(readingIndex, nextKanaIndex)}</rt></ruby>`
          readingIndex = nextKanaIndex
        }
      }
      return result
    },
    /**
     * 对文本内容应用所有注音转换规则
     * @param {string} text - 原始文本节点内容
     * @returns {string} - 转换后的 HTML 字符串
     */
    _processTextContent(text) {
      if (!text.includes('（') && !text.includes('(')) return text

      // 优先级 1 & 2: 应用预处理的特例词和动态学习的词汇
      if (this._processedWords.globalRegex) {
        text = text.replace(this._processedWords.globalRegex, (match) => this._processedWords.patternResults.get(match) || match)
      }

      // 优先级 3: 处理片假名(英文)模式
      text = text.replace(this._regex.katakana, (_, katakana, romaji) => `<ruby>${katakana}<rt>${romaji}</rt></ruby>`)

      // 优先级 4: 处理通用汉字(注音)模式，并应用排除规则
      return text.replace(this._regex.ruby, (match, kanji, reading) => {
        const fullMatch = `${kanji}（${reading}）`
        // 执行各项检查
        if (this._rules.EXCLUDE.STRINGS.has(fullMatch) || (this._rules.EXCLUDE.PARTICLES.has(reading) && this._regex.kanaOnly.test(kanji)) || this._regex.nonKana.test(reading)) {
          return match // 不满足条件，返回原文
        }
        return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : match
      })
    },
    /**
     * 应用 HTML 级别的替换规则 (优先级 0)
     * @param {HTMLElement} element - 目标元素
     */
    _applyHtmlReplacements(element) {
      let html = element.innerHTML
      this._rules.HTML.forEach((rule) => {
        html = html.replace(rule.pattern, rule.replacement)
      })
      if (html !== element.innerHTML) element.innerHTML = html
    },
    /**
     * 扫描并学习新的复合词条 (优先级 2)
     * @param {HTMLElement} element - 需要扫描的元素
     */
    _findAndRegisterCompounds(element) {
      const html = element.innerHTML
      const newPatterns = []

      for (const match of html.matchAll(this._regex.bracket)) {
        // 直接从捕获组获取数据
        const kanjiPart = match[1] // 捕获组 1: 汉字部分
        const readingPart = match[2] // 捕获组 2: 读音部分
        const suffixPart = match[3] // 捕获组 3: 后缀部分
        // 进行有效性检查
        if (
          match[0].includes('＋') ||
          match[0].includes('+') ||
          this._regex.nonKana.test(readingPart) || // 规则 1：读音部分必须是纯假名
          this._regex.hasInvalidChars.test(kanjiPart) // 规则 2：汉字部分不能含有特殊符号
        ) {
          continue
        }
        // 组合成完整的模式，用作 Map 的键
        const fullPattern = `${kanjiPart}（${readingPart}）${suffixPart}`
        // 如果是新词，则学习它
        if (!this._dynamicWords.has(fullPattern)) {
          this._dynamicWords.add(fullPattern)
          // 直接使用捕获到的部分生成 ruby HTML，并附加后缀
          const rubyHtml = this._segmentCompoundWord(kanjiPart, readingPart) + suffixPart
          // 将转换结果存入缓存，并准备更新全局正则
          this._processedWords.patternResults.set(fullPattern, rubyHtml)
          newPatterns.push(this._escapeRegExp(fullPattern))
        }
      }

      // 如果有新学习的词汇，则更新全局正则表达式
      if (newPatterns.length > 0) {
        const existing = this._processedWords.globalRegex ? this._processedWords.globalRegex.source.slice(1, -2).split('|') : []
        this._rebuildGlobalRegex([...existing, ...newPatterns])
      }
    },
    /**
     * 遍历 DOM，将文本节点批量转换为包含 Ruby 标签的 HTML
     * @param {HTMLElement} root - 根元素
     */
    _processRubyInNodes(root) {
      // 使用 TreeWalker 高效遍历所有文本节点
      const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
        acceptNode: (n) => (n.parentNode.nodeName !== 'SCRIPT' && n.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
      })
      const nodesToProcess = []
      let node
      // 收集所有需要修改的文本节点
      while ((node = walker.nextNode())) {
        const newContent = this._processTextContent(node.nodeValue)
        if (newContent !== node.nodeValue) nodesToProcess.push({ node, newContent })
      }
      // 从后往前批量替换，避免 DOM 位置错乱
      for (let i = nodesToProcess.length - 1; i >= 0; i--) {
        const { node, newContent } = nodesToProcess[i]
        // 使用 Range API 高效地将 HTML 字符串替换文本节点
        node.parentNode.replaceChild(document.createRange().createContextualFragment(newContent), node)
      }
    },
  }

  /**
   * @module SettingsPanel
   * @description 管理设置面板的创建、事件处理和状态持久化
   * 此模块负责在页面上创建一个浮动设置面板，允许用户动态地开关脚本功能和调整振假名 (Furigana) 的可见性
   * 所有设置都会通过 GM_setValue/GM_getValue 进行持久化保存
   */
  const SettingsPanel = {
    _config: {
      MODULE_ENABLED: true, // 是否启用设置面板
      FEEDBACK_URL: 'https://greasyfork.org/scripts/542386-edewakaru-enhanced',
      OPTIONS: {
        SCRIPT_ENABLED: { label: 'ページ最適化', defaultValue: true, handler: '_handleScriptToggle', isChild: false },
        FURIGANA_VISIBLE: { label: '振り仮名表示', defaultValue: true, handler: '_handleFuriganaToggle', isChild: true },
        IFRAME_LOAD_ENABLED: { label: '関連記事表示', defaultValue: true, handler: '_handleIframeLoadToggle', isChild: true },
        TTS_ENABLED: { label: '単語選択発音', defaultValue: false, handler: '_handleTtsToggle', isChild: true },
      },
      STYLES: `
        #settings-panel { position: fixed; bottom: 24px; right: 24px; z-index: 9999; display: flex; flex-direction: column; gap: 8px; padding: 16px; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.9; -webkit-user-select: none; user-select: none; }
        .settings-title { font-size: 14px; font-weight: 600; color: #1F2937; margin: 0 0 6px 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 6px; position: relative; }
        .feedback-link, .feedback-link:visited { position: absolute; top: 0; right: 0; width: 16px; height: 16px; color: #E5E7EB !important; transition: color 0.2s ease-in-out; }
        .feedback-link:hover { color: #3B82F6 !important; }
        .feedback-link svg { width: 100%; height: 100%; }
        .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 8px; }
        .setting-label { font-size: 13px; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
        .toggle-switch { position: relative; display: inline-block; width: 40px; height: 20px; flex-shrink: 0; }
        .toggle-switch.disabled { opacity: 0.5; pointer-events: none; }
        .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; }
        .toggle-slider:before { position: absolute; content: ""; height: 15px; width: 15px; left: 2.5px; bottom: 2.5px; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
        input:checked+.toggle-slider { background-color: #3B82F6; }
        input:checked+.toggle-slider:before { transform: translateX(20px); }
        .settings-notification { position: fixed; bottom: 208px; right: 24px; z-index: 9999; padding: 8px 12px; background-color: #3B82F6; color: white; border-radius: 6px; font-size: 13px; animation: slideInOut 3s ease-in-out; -webkit-user-select: none; user-select: none; }
        @keyframes slideInOut { 0%, 100% { opacity: 0; transform: translateX(20px); } 15%, 85% { opacity: 0.9; transform: translateX(0); } }
      `,
    },
    // 用于缓存 UI 元素，避免重复查询 DOM
    _uiElements: {},
    init() {
      GM_addStyle(this._config.STYLES)
      this._createPanel()
      this._initializeFuriganaDisplay()
    },
    /**
     * 从存储中获取所有设置的原始值。
     * @returns {object} - 一个包含所有选项当前值的对象，键名保持全大写。
     */
    getOptions() {
      const options = {}
      for (const key in this._config.OPTIONS) {
        options[key] = GM_getValue(key, this._config.OPTIONS[key].defaultValue)
      }
      return options
    },
    // 事件处理器：处理主开关切换
    _handleScriptToggle(enabled) {
      GM_setValue('SCRIPT_ENABLED', enabled)
      this._showNotification()
      this._updateChildOptionsUI(enabled)
    },
    // 事件处理器：处理振假名可见性切换
    _handleFuriganaToggle(visible) {
      GM_setValue('FURIGANA_VISIBLE', visible)
      this._toggleFuriganaDisplay(visible)
    },
    // 事件处理器：处理 iframe 加载切换
    _handleIframeLoadToggle(enabled) {
      GM_setValue('IFRAME_LOAD_ENABLED', enabled)
      this._showNotification()
    },
    // 事件处理器：处理 TTS 开关切换
    _handleTtsToggle(enabled) {
      GM_setValue('TTS_ENABLED', enabled)
      // TODO：耦合需要优化
      enabled ? ContextMenu.init() : ContextMenu.destroy()
    },
    // 切换振假名显示状态
    _toggleFuriganaDisplay(visible) {
      const id = 'furigana-display-style'
      let style = document.getElementById(id)
      if (!style) {
        style = document.createElement('style')
        style.id = id
        document.head.appendChild(style)
      }
      style.textContent = `rt { display: ${visible ? 'ruby-text' : 'none'} !important; }`
    },
    // 根据保存的设置初始化振假名显示状态
    _initializeFuriganaDisplay() {
      // 因为注音默认就是可见的，不需要处理
      if (!GM_getValue('FURIGANA_VISIBLE', this._config.OPTIONS.FURIGANA_VISIBLE.defaultValue)) {
        this._toggleFuriganaDisplay(false)
      }
    },
    _createPanel() {
      if (!this._config.MODULE_ENABLED) return
      const panel = document.createElement('div')
      panel.id = 'settings-panel'
      panel.innerHTML = `
        <h3 class="settings-title">
          設定パネル
          <a href="${this._config.FEEDBACK_URL}" target="_blank" rel="noopener noreferrer" class="feedback-link" title="Feedback">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
          </a>
        </h3>
      `
      const isMasterEnabled = GM_getValue('SCRIPT_ENABLED', this._config.OPTIONS.SCRIPT_ENABLED.defaultValue)
      // 直接遍历 _config.OPTIONS 来创建所有 UI 开关
      for (const key in this._config.OPTIONS) {
        const config = this._config.OPTIONS[key]
        let isDisabled = config.isChild && !isMasterEnabled
        if (key === 'TTS_ENABLED' && !('speechSynthesis' in window)) {
          isDisabled = true
        }
        panel.appendChild(this._createToggle(key, config, isDisabled))
      }
      document.body.appendChild(panel)
    },
    _createToggle(key, config, isDisabled) {
      const { label, handler, defaultValue } = config
      const id = `setting-${key.toLowerCase()}`
      const itemContainer = document.createElement('div')
      itemContainer.className = 'setting-item'
      itemContainer.dataset.key = key

      const isChecked = GM_getValue(key, defaultValue)
      itemContainer.innerHTML = `
        <label for="${id}" class="setting-label">${label}</label>
        <label class="toggle-switch ${isDisabled ? 'disabled' : ''}">
          <input type="checkbox" id="${id}" ${isChecked ? 'checked' : ''}>
          <span class="toggle-slider"></span>
        </label>
      `
      const toggleSwitch = itemContainer.querySelector('.toggle-switch')
      this._uiElements[key] = { switch: toggleSwitch }
      itemContainer.querySelector('input').addEventListener('change', (e) => this[handler](e.target.checked))
      return itemContainer
    },
    _updateChildOptionsUI(masterEnabled) {
      for (const key in this._config.OPTIONS) {
        if (this._config.OPTIONS[key].isChild) {
          const uiElement = this._uiElements[key]
          if (uiElement && uiElement.switch) {
            uiElement.switch.classList.toggle('disabled', !masterEnabled)
          }
        }
      }
    },
    // 显示通知
    _showNotification(message = '設定を保存しました。再読み込みしてください。') {
      const el = document.createElement('div')
      el.className = 'settings-notification'
      el.textContent = message
      document.body.appendChild(el)
      setTimeout(() => el.remove(), 3000)
    },
  }

  /**
   * @module TTSPlayer
   * @description
   */
  const TTSPlayer = {
    _config: {
      VOICE_NAMES: ['Microsoft Nanami Online (Natural) - Japanese (Japan)', 'Microsoft Keita Online (Natural) - Japanese (Japan)'],
      LANG: 'ja-JP',
    },

    _initPromise: null,
    _voices: [],

    async speak(text) {
      if (!this._initPromise) {
        this._initPromise = this._initialize()
      }
      await this._initPromise
      speechSynthesis.cancel()
      if (!text?.trim() || this._voices.length === 0) {
        this._voices.length === 0 && console.warn('[TTSPlayer] TTS is unavailable')
        return
      }
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.voice = this._voices[Math.floor(Math.random() * this._voices.length)]
      utterance.lang = this._config.LANG
      utterance.onerror = (e) => {
        !['canceled', 'interrupted'].includes(e.error) && console.error(`[TTSPlayer] TTS playback error: ${e.error}`)
      }
      speechSynthesis.speak(utterance)
    },

    _initialize() {
      return new Promise((resolve) => {
        if (!('speechSynthesis' in window)) {
          console.warn('[TTSPlayer] SpeechSynthesis is not supported')
          return resolve()
        }
        let resolved = false

        const loadVoices = () => {
          if (resolved) return
          resolved = true
          const allVoices = speechSynthesis.getVoices()
          const { VOICE_NAMES, LANG } = this._config
          this._voices = allVoices.filter((v) => VOICE_NAMES.includes(v.name) && v.lang === LANG)
          console.warn(this._voices.length > 0 ? '[TTSPlayer] Native TTS is available' : '[TTSPlayer] No native voice found, TTS is unavailable')
          // 清理事件监听器
          speechSynthesis.onvoiceschanged = null
          resolve()
        }

        // 三重保险机制
        const initialVoices = speechSynthesis.getVoices()
        if (initialVoices.length > 0) {
          loadVoices()
        } else {
          speechSynthesis.onvoiceschanged = loadVoices
          setTimeout(loadVoices, 500)
        }
      })
    },
  }

  /**
   * @module ContextMenu
   * @description 管理划词出现的TTS快捷菜单
   */
  const ContextMenu = {
    _config: {
      MODULE_ENABLED: true,
      MENU_ID: 'selection-context-menu',
      STYLES: `
        #selection-context-menu { position: absolute; top: 0; left: 0; display: none; z-index: 9999; opacity: 0; transition: opacity 0.1s ease-out, transform 0.1s ease-out; user-select: none; will-change: transform, opacity; }
        #selection-context-menu.visible { opacity: 0.9; }
        #selection-context-menu button { display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0; border-radius: 50%; cursor: grab; border: none; background-color: #3B82F6; color: #FFFFFF; box-shadow: 0 5px 15px rgba(0,0,0,0.15), 0 2px 5px rgba(0,0,0,0.1); transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out; }
        #selection-context-menu button:hover { background-color: #4B90F8; transform: scale(1.1); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3); }
        #selection-context-menu button:active { cursor: grabbing; }
        #selection-context-menu button svg { width: 20px; height: 20px; stroke: currentColor; stroke-width: 2; pointer-events: none; }
      `,
    },
    menuElement: null,
    isDragging: false,
    dragUpdatePending: false,
    // 统一使用 lastPosX/Y 记录图标的最终 transformX/Y 值
    lastPosX: 0,
    lastPosY: 0,
    // dragStartX/Y 记录的是鼠标与图标位置的偏移
    dragOffsetX: 0,
    dragOffsetY: 0,
    boundHandleDragStart: null,
    boundHandleMouseUp: null,
    boundDragMove: null,
    boundDragEnd: null,
    boundTransitionEnd: null,

    init(options) {
      if (!this._config.MODULE_ENABLED) return
      if (this.menuElement) return
      Object.assign(this._config, options)
      GM_addStyle(this._config.STYLES)
      this._createMenu()
      this._bindEvents()
    },

    destroy() {
      if (!this.menuElement) return
      this.menuElement.remove()
      this.menuElement = null
      document.removeEventListener('mouseup', this.boundHandleMouseUp)
      document.removeEventListener('mousemove', this.boundDragMove)
      document.removeEventListener('mouseup', this.boundDragEnd)
    },

    _createMenu() {
      if (document.getElementById(this._config.MENU_ID)) return
      this.menuElement = document.createElement('div')
      this.menuElement.id = this._config.MENU_ID
      const readButton = document.createElement('button')
      readButton.title = 'Read'
      readButton.setAttribute('aria-label', 'Read selected text')
      readButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>`
      readButton.addEventListener('click', (event) => {
        if (this.isDragging) {
          event.stopPropagation()
          return
        }
        const cleanedText = this._getCleanedSelectedText()
        if (cleanedText) {
          // TODO：耦合需要优化
          TTSPlayer.speak(cleanedText)
        }
      })
      this.menuElement.appendChild(readButton)
      document.body.appendChild(this.menuElement)
    },

    _getCleanedSelectedText() {
      const selection = window.getSelection()
      if (!selection || selection.rangeCount === 0) return ''
      const tempContainer = document.createElement('div')
      tempContainer.appendChild(selection.getRangeAt(0).cloneContents())
      // 1. 移除注音
      tempContainer.querySelectorAll('rt').forEach((el) => el.remove())
      // 2. 替换换行
      tempContainer.querySelectorAll('br').forEach((el) => el.replaceWith('。'))
      // 3. 剥离所有 HTML 标签并获取纯文本
      let text = tempContainer.textContent
      // 4. 替换 Emoji 为句号
      const emojiRegex = /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu
      text = text.replace(emojiRegex, '。')
      // 5. 清理空白
      text = text.replace(/\s+/g, '')
      return text
    },

    _bindEvents() {
      this.boundHandleDragStart = this._handleDragStart.bind(this)
      this.boundHandleMouseUp = this._handleMouseUp.bind(this)
      this.boundDragMove = this._handleDragMove.bind(this)
      this.boundDragEnd = this._handleDragEnd.bind(this)
      this.boundTransitionEnd = this._onTransitionEnd.bind(this)
      this.menuElement.addEventListener('mousedown', this.boundHandleDragStart)
      this.menuElement.addEventListener('transitionend', this.boundTransitionEnd)
      document.addEventListener('mouseup', this.boundHandleMouseUp)
    },

    // 采用更稳定可靠的 offset 拖拽逻辑，并使用 pageX/Y
    _handleDragStart(event) {
      event.preventDefault()
      event.stopPropagation()
      this.isDragging = false
      this.dragOffsetX = event.pageX - this.lastPosX
      this.dragOffsetY = event.pageY - this.lastPosY
      this.menuElement.style.transition = 'none' // 拖拽时禁用动画
      document.addEventListener('mousemove', this.boundDragMove)
      document.addEventListener('mouseup', this.boundDragEnd, { once: true })
    },

    _handleDragMove(event) {
      event.preventDefault()
      if (this.dragUpdatePending) return
      this.dragUpdatePending = true
      requestAnimationFrame(() => {
        this.isDragging = true
        // 使用正确的 pageX/Y 和 offset 计算
        this.lastPosX = event.pageX - this.dragOffsetX
        this.lastPosY = event.pageY - this.dragOffsetY
        this.menuElement.style.transform = `translate(${this.lastPosX}px, ${this.lastPosY}px)`
        this.dragUpdatePending = false
      })
    },

    _handleDragEnd() {
      document.removeEventListener('mousemove', this.boundDragMove)
      this.menuElement.style.transition = '' // 恢复动画
      setTimeout(() => {
        this.isDragging = false
      }, 0)
    },

    _handleMouseUp(event) {
      if (this.isDragging || this.menuElement.contains(event.target)) {
        return
      }
      setTimeout(() => {
        const selectedText = window.getSelection().toString().trim()
        if (selectedText.length > 0) {
          this._showMenu(event.pageX, event.pageY)
        } else {
          this._hideMenu()
        }
      }, 10)
    },

    _showMenu(x, y) {
      if (!this.menuElement) return
      this.lastPosX = x + 8
      this.lastPosY = y + 8
      this.menuElement.style.transition = 'none'
      this.menuElement.style.transform = `translate(${this.lastPosX}px, ${this.lastPosY}px)`
      this.menuElement.style.display = 'block'
      requestAnimationFrame(() => {
        this.menuElement.style.transition = ''
        this.menuElement.classList.add('visible')
      })
    },

    _hideMenu() {
      if (!this.menuElement || !this.menuElement.classList.contains('visible')) return
      this.menuElement.classList.remove('visible')
    },

    _onTransitionEnd() {
      if (this.menuElement && !this.menuElement.classList.contains('visible')) {
        this.menuElement.style.display = 'none'
      }
    },
  }
  /**
   * @module MainController
   * @description 脚本主控制器，负责协调所有模块的初始化和执行流程
   */
  const MainController = {
    run() {
      // 从 SettingsPanel 获取所有持久化的用户设置
      const options = SettingsPanel.getOptions()

      // 步骤 1: 检查脚本是否被用户禁用如果禁用，则仅加载设置面板，不执行任何页面修改
      if (!options.SCRIPT_ENABLED) {
        document.addEventListener('DOMContentLoaded', () => SettingsPanel.init())
        return
      }

      // 步骤 2: 在 document-start 阶段，立即执行不依赖 DOM 内容的操作，以尽快生效
      PageOptimizer.init() // 注入样式，防止页面闪烁
      RubyConverter.init(RULES) // 预处理词库，为后续操作做准备

      // 步骤 3: 等待 DOM 完全加载后，执行依赖 DOM 内容的操作
      document.addEventListener('DOMContentLoaded', () => {
        PageOptimizer.cleanupGlobalElements()
        IframeLoader.init(options)
        SettingsPanel.init()
        if (options.TTS_ENABLED) ContextMenu.init()
        this._processPageContent()
      })
    },

    /**
     * 编排所有对页面主要内容的处理流程
     */
    _processPageContent() {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length === 0) {
        // 即使没有文章，也需要执行最终布局，以显示侧边栏
        PageOptimizer.finalizeLayout()
        return
      }

      let currentIndex = 0
      /**
       * 编排所有对页面主要内容的处理流程
       * 此方法采用异步分批处理（Asynchronous Batch Processing）的策略
       * 以避免在处理包含大量文章的长页面时，因脚本长时间占用主线程而导致的页面卡顿或无响应
       */
      const processBatch = () => {
        // 定义批次大小，每次处理 2 篇文章
        // Math.min 确保最后一批不会超出数组范围
        const batchSize = Math.min(2, articleBodies.length - currentIndex)
        const endIndex = currentIndex + batchSize

        // 在当前帧内，同步处理本批次的所有文章
        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i]
          // 1. 最先处理，确保在最原始的 DOM 上工作
          RubyConverter.processContainer(body)
          // 2. 替换iframe为占位符
          IframeLoader.processContainer(body)
          // 3. 替换图片链接
          ImageProcessor.process(body)
          // 4. 最后做收尾清理和显示
          PageOptimizer.cleanupArticleBody(body)
        }
        // 将索引移动到下一批次的起始位置
        currentIndex = endIndex
        // 检查是否还有未处理的文章
        if (currentIndex < articleBodies.length) {
          // 使用 requestAnimationFrame 请求浏览器在下一次重绘前调用 processBatch
          requestAnimationFrame(processBatch)
        } else {
          // 所有批次处理完成，执行最终的全局布局调整
          PageOptimizer.finalizeLayout()
        }
      }
      // 启动第一个批次的处理
      requestAnimationFrame(processBatch)
    },
  }
  MainController.run()
})()
